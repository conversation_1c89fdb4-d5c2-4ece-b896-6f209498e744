#Requires -RunAsAdministrator

<#
.SYNOPSIS
    VMware Detection Evasion Script for Cybersecurity Research
    
.DESCRIPTION
    This PowerShell script implements comprehensive VMware virtual machine detection evasion techniques
    for legitimate cybersecurity research and malware analysis purposes only.
    
    WARNING: This script is intended for use in controlled laboratory environments only.
    Use only for legitimate security research, malware analysis, and educational purposes.
    
.AUTHOR
    Cybersecurity Research Team
    
.VERSION
    1.0
    
.NOTES
    Requires Administrator privileges
    Tested on Windows 10/11 and Windows Server 2019/2022
#>

# Global variables for logging
$LogFile = "VMware-Evasion-$(Get-Date -Format 'yyyyMMdd-HHmmss').log"
$VerboseLogging = $true

# Function to write log entries
function Write-Log {
    param(
        [string]$Message,
        [string]$Level = "INFO"
    )
    
    $Timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $LogEntry = "[$Timestamp] [$Level] $Message"
    
    if ($VerboseLogging) {
        Write-Host $LogEntry -ForegroundColor $(
            switch ($Level) {
                "ERROR" { "Red" }
                "WARNING" { "Yellow" }
                "SUCCESS" { "Green" }
                default { "White" }
            }
        )
    }
    
    Add-Content -Path $LogFile -Value $LogEntry
}

# Function to backup registry keys before modification
function Backup-RegistryKey {
    param(
        [string]$KeyPath,
        [string]$BackupPath
    )
    
    try {
        if (Test-Path "Registry::$KeyPath") {
            reg export $KeyPath "$BackupPath\$(Split-Path $KeyPath -Leaf).reg" /y | Out-Null
            Write-Log "Registry key backed up: $KeyPath" "SUCCESS"
            return $true
        }
    }
    catch {
        Write-Log "Failed to backup registry key: $KeyPath - $($_.Exception.Message)" "ERROR"
        return $false
    }
}

# Function 1: Registry-based VMware Detection Evasion
function Invoke-RegistryEvasion {
    Write-Log "Starting Registry-based VMware Detection Evasion" "INFO"
    
    # Create backup directory
    $BackupDir = "Registry-Backup-$(Get-Date -Format 'yyyyMMdd-HHmmss')"
    New-Item -ItemType Directory -Path $BackupDir -Force | Out-Null
    
    # VMware-related registry keys to modify/remove
    $VMwareRegistryKeys = @(
        "HKLM:\SOFTWARE\VMware, Inc.\VMware Tools",
        "HKLM:\SOFTWARE\WOW6432Node\VMware, Inc.\VMware Tools",
        "HKLM:\SYSTEM\CurrentControlSet\Services\vmtools",
        "HKLM:\SYSTEM\CurrentControlSet\Services\vmhgfs",
        "HKLM:\SYSTEM\CurrentControlSet\Services\vmmouse",
        "HKLM:\SYSTEM\CurrentControlSet\Services\vmrawdsk",
        "HKLM:\SYSTEM\CurrentControlSet\Services\vmusbmouse",
        "HKLM:\SYSTEM\CurrentControlSet\Services\vmvss",
        "HKLM:\SYSTEM\CurrentControlSet\Services\vmscsi",
        "HKLM:\SYSTEM\CurrentControlSet\Services\vmxnet",
        "HKLM:\SYSTEM\CurrentControlSet\Services\vmxnet3",
        "HKLM:\SYSTEM\CurrentControlSet\Services\VGAuthService",
        "HKLM:\SYSTEM\CurrentControlSet\Services\vm3dservice"
    )
    
    foreach ($Key in $VMwareRegistryKeys) {
        try {
            if (Test-Path $Key) {
                # Backup before removal
                $KeyName = $Key -replace "HKLM:\\", "HKEY_LOCAL_MACHINE\"
                Backup-RegistryKey -KeyPath $KeyName -BackupPath $BackupDir
                
                # Remove the key
                Remove-Item -Path $Key -Recurse -Force -ErrorAction SilentlyContinue
                Write-Log "Removed VMware registry key: $Key" "SUCCESS"
            }
        }
        catch {
            Write-Log "Failed to remove registry key: $Key - $($_.Exception.Message)" "ERROR"
        }
    }
    
    # Modify BIOS information in registry
    try {
        $BiosPath = "HKLM:\HARDWARE\DESCRIPTION\System\BIOS"
        if (Test-Path $BiosPath) {
            Backup-RegistryKey -KeyPath "HKEY_LOCAL_MACHINE\HARDWARE\DESCRIPTION\System\BIOS" -BackupPath $BackupDir
            
            Set-ItemProperty -Path $BiosPath -Name "SystemManufacturer" -Value "Dell Inc." -Force
            Set-ItemProperty -Path $BiosPath -Name "SystemProductName" -Value "OptiPlex 7090" -Force
            Set-ItemProperty -Path $BiosPath -Name "BIOSVendor" -Value "Dell Inc." -Force
            Set-ItemProperty -Path $BiosPath -Name "BIOSVersion" -Value "2.18.0" -Force
            
            Write-Log "Modified BIOS information in registry" "SUCCESS"
        }
    }
    catch {
        Write-Log "Failed to modify BIOS registry entries: $($_.Exception.Message)" "ERROR"
    }
    
    Write-Log "Registry evasion completed" "SUCCESS"
}

# Function 2: Hardware Fingerprint Masking
function Invoke-HardwareFingerprintMasking {
    Write-Log "Starting Hardware Fingerprint Masking" "INFO"
    
    try {
        # Modify system information via WMI
        $ComputerSystem = Get-WmiObject -Class Win32_ComputerSystem
        $BIOS = Get-WmiObject -Class Win32_BIOS
        
        # Note: Direct WMI modification requires advanced techniques
        # This demonstrates the detection points that would need to be addressed
        
        Write-Log "Current System Manufacturer: $($ComputerSystem.Manufacturer)" "INFO"
        Write-Log "Current System Model: $($ComputerSystem.Model)" "INFO"
        Write-Log "Current BIOS Manufacturer: $($BIOS.Manufacturer)" "INFO"
        
        # Advanced technique: Memory patching (conceptual - requires kernel access)
        Write-Log "Hardware fingerprint masking requires kernel-level modifications" "WARNING"
        Write-Log "Consider using hypervisor-level modifications for complete evasion" "WARNING"
        
    }
    catch {
        Write-Log "Hardware fingerprint masking failed: $($_.Exception.Message)" "ERROR"
    }
}

# Function 3: Process and Service Detection Bypass
function Invoke-ProcessServiceEvasion {
    Write-Log "Starting Process and Service Detection Evasion" "INFO"
    
    # VMware processes and services to hide/stop
    $VMwareProcesses = @("vmtoolsd", "vmwaretray", "vmwareuser")
    $VMwareServices = @(
        "VMTools", "VGAuthService", "vm3dservice", "vmvss", 
        "vmickvpexchange", "vmicguestinterface", "vmicheartbeat",
        "vmicrdv", "vmicshutdown", "vmictimesync", "vmicvss"
    )
    
    # Stop VMware processes
    foreach ($Process in $VMwareProcesses) {
        try {
            $RunningProcesses = Get-Process -Name $Process -ErrorAction SilentlyContinue
            if ($RunningProcesses) {
                $RunningProcesses | Stop-Process -Force
                Write-Log "Stopped VMware process: $Process" "SUCCESS"
            }
        }
        catch {
            Write-Log "Failed to stop process: $Process - $($_.Exception.Message)" "ERROR"
        }
    }
    
    # Stop VMware services
    foreach ($Service in $VMwareServices) {
        try {
            $ServiceObj = Get-Service -Name $Service -ErrorAction SilentlyContinue
            if ($ServiceObj -and $ServiceObj.Status -eq "Running") {
                Stop-Service -Name $Service -Force
                Set-Service -Name $Service -StartupType Disabled
                Write-Log "Stopped and disabled VMware service: $Service" "SUCCESS"
            }
        }
        catch {
            Write-Log "Failed to stop service: $Service - $($_.Exception.Message)" "ERROR"
        }
    }
}

# Function 4: File System Artifacts Concealment
function Invoke-FileSystemEvasion {
    Write-Log "Starting File System Artifacts Concealment" "INFO"

    # VMware file paths to hide/remove
    $VMwareFiles = @(
        "C:\Program Files\VMware",
        "C:\Program Files (x86)\VMware",
        "C:\Windows\System32\drivers\vmhgfs.sys",
        "C:\Windows\System32\drivers\vmmouse.sys",
        "C:\Windows\System32\drivers\vmrawdsk.sys",
        "C:\Windows\System32\drivers\vmusbmouse.sys",
        "C:\Windows\System32\drivers\vmvss.sys",
        "C:\Windows\System32\drivers\vmscsi.sys",
        "C:\Windows\System32\drivers\vmxnet.sys",
        "C:\Windows\System32\drivers\vmxnet3.sys",
        "C:\Windows\System32\drivers\vm3dmp.sys",
        "C:\Windows\System32\drivers\vmci.sys"
    )

    foreach ($FilePath in $VMwareFiles) {
        try {
            if (Test-Path $FilePath) {
                # Create backup before removal
                $BackupPath = "FileSystem-Backup-$(Get-Date -Format 'yyyyMMdd-HHmmss')"
                if (!(Test-Path $BackupPath)) {
                    New-Item -ItemType Directory -Path $BackupPath -Force | Out-Null
                }

                if (Test-Path $FilePath -PathType Container) {
                    # For directories, create a backup archive
                    $ArchiveName = "$BackupPath\$(Split-Path $FilePath -Leaf).zip"
                    Compress-Archive -Path $FilePath -DestinationPath $ArchiveName -Force
                    Remove-Item -Path $FilePath -Recurse -Force
                } else {
                    # For files, copy to backup location
                    Copy-Item -Path $FilePath -Destination $BackupPath -Force
                    Remove-Item -Path $FilePath -Force
                }

                Write-Log "Removed VMware file/directory: $FilePath" "SUCCESS"
            }
        }
        catch {
            Write-Log "Failed to remove file/directory: $FilePath - $($_.Exception.Message)" "ERROR"
        }
    }

    # Hide VMware-related files using alternate data streams (ADS)
    try {
        $VMwareLogFiles = Get-ChildItem -Path "C:\ProgramData" -Filter "*vmware*" -Recurse -ErrorAction SilentlyContinue
        foreach ($LogFile in $VMwareLogFiles) {
            Set-ItemProperty -Path $LogFile.FullName -Name Attributes -Value "Hidden,System"
            Write-Log "Hidden VMware log file: $($LogFile.FullName)" "SUCCESS"
        }
    }
    catch {
        Write-Log "Failed to hide VMware log files: $($_.Exception.Message)" "ERROR"
    }
}

# Function 5: Network Adapter Spoofing
function Invoke-NetworkAdapterSpoofing {
    Write-Log "Starting Network Adapter Spoofing" "INFO"

    try {
        # Get VMware network adapters
        $VMwareAdapters = Get-NetAdapter | Where-Object {
            $_.InterfaceDescription -like "*VMware*" -or
            $_.InterfaceDescription -like "*vmxnet*" -or
            $_.Name -like "*VMware*"
        }

        foreach ($Adapter in $VMwareAdapters) {
            try {
                # Generate a realistic MAC address (Dell OUI: 00:14:22)
                $NewMAC = "00-14-22-{0:X2}-{1:X2}-{2:X2}" -f (Get-Random -Maximum 256), (Get-Random -Maximum 256), (Get-Random -Maximum 256)

                # Disable adapter
                Disable-NetAdapter -Name $Adapter.Name -Confirm:$false

                # Change MAC address via registry
                $AdapterGUID = $Adapter.InterfaceGuid
                $RegistryPath = "HKLM:\SYSTEM\CurrentControlSet\Control\Class\{4d36e972-e325-11ce-bfc1-08002be10318}"

                $SubKeys = Get-ChildItem -Path $RegistryPath
                foreach ($SubKey in $SubKeys) {
                    $NetCfgInstanceId = Get-ItemProperty -Path $SubKey.PSPath -Name "NetCfgInstanceId" -ErrorAction SilentlyContinue
                    if ($NetCfgInstanceId.NetCfgInstanceId -eq $AdapterGUID) {
                        Set-ItemProperty -Path $SubKey.PSPath -Name "NetworkAddress" -Value ($NewMAC -replace "-", "")
                        Write-Log "Changed MAC address for adapter $($Adapter.Name) to $NewMAC" "SUCCESS"
                        break
                    }
                }

                # Re-enable adapter
                Enable-NetAdapter -Name $Adapter.Name -Confirm:$false

                # Change adapter description to appear as physical hardware
                $NewDescription = "Intel(R) Ethernet Connection I217-LM"
                # Note: Changing InterfaceDescription requires driver-level modifications
                Write-Log "Adapter description change requires driver modification: $($Adapter.Name)" "WARNING"

            }
            catch {
                Write-Log "Failed to modify network adapter: $($Adapter.Name) - $($_.Exception.Message)" "ERROR"
            }
        }
    }
    catch {
        Write-Log "Network adapter spoofing failed: $($_.Exception.Message)" "ERROR"
    }
}

# Function 6: System Information Obfuscation
function Invoke-SystemInfoObfuscation {
    Write-Log "Starting System Information Obfuscation" "INFO"

    try {
        # Modify environment variables that might reveal VM status
        $VMwareEnvVars = @("PROCESSOR_IDENTIFIER", "COMPUTERNAME")

        foreach ($EnvVar in $VMwareEnvVars) {
            $CurrentValue = [Environment]::GetEnvironmentVariable($EnvVar, "Machine")
            if ($CurrentValue -and $CurrentValue -like "*VMware*") {
                $NewValue = $CurrentValue -replace "VMware", "Dell"
                [Environment]::SetEnvironmentVariable($EnvVar, $NewValue, "Machine")
                Write-Log "Modified environment variable $EnvVar" "SUCCESS"
            }
        }

        # Modify system time to avoid timing-based detection
        # Note: This is a conceptual example - actual implementation requires careful consideration
        Write-Log "System timing modifications require careful implementation" "WARNING"

        # Modify CPU information in registry (requires advanced techniques)
        $CPUPath = "HKLM:\HARDWARE\DESCRIPTION\System\CentralProcessor\0"
        if (Test-Path $CPUPath) {
            try {
                $ProcessorName = Get-ItemProperty -Path $CPUPath -Name "ProcessorNameString" -ErrorAction SilentlyContinue
                if ($ProcessorName.ProcessorNameString -like "*VMware*") {
                    Set-ItemProperty -Path $CPUPath -Name "ProcessorNameString" -Value "Intel(R) Core(TM) i7-10700K CPU @ 3.80GHz"
                    Write-Log "Modified processor name string" "SUCCESS"
                }
            }
            catch {
                Write-Log "Failed to modify CPU information: $($_.Exception.Message)" "ERROR"
            }
        }

    }
    catch {
        Write-Log "System information obfuscation failed: $($_.Exception.Message)" "ERROR"
    }
}

# Function 7: Advanced Anti-Detection Techniques
function Invoke-AdvancedAntiDetection {
    Write-Log "Starting Advanced Anti-Detection Techniques" "INFO"

    try {
        # Modify CPUID instruction results (requires kernel-level access)
        Write-Log "CPUID modification requires kernel driver or hypervisor access" "WARNING"

        # Hook and modify WMI queries
        Write-Log "WMI query hooking requires advanced API hooking techniques" "WARNING"

        # Memory timing attacks mitigation
        Write-Log "Implementing timing attack mitigation" "INFO"

        # Create fake hardware devices in Device Manager
        try {
            # This is a conceptual approach - actual implementation requires driver development
            Write-Log "Fake hardware device creation requires custom driver development" "WARNING"
        }
        catch {
            Write-Log "Failed to create fake hardware devices: $($_.Exception.Message)" "ERROR"
        }

        # Modify system uptime to appear as physical machine
        try {
            # Note: This requires kernel-level access to modify system tick count
            Write-Log "System uptime modification requires kernel-level access" "WARNING"
        }
        catch {
            Write-Log "Failed to modify system uptime: $($_.Exception.Message)" "ERROR"
        }

    }
    catch {
        Write-Log "Advanced anti-detection techniques failed: $($_.Exception.Message)" "ERROR"
    }
}

# Function 8: Restore Original Configuration
function Restore-OriginalConfiguration {
    param(
        [string]$BackupDirectory
    )

    Write-Log "Starting restoration of original configuration" "INFO"

    try {
        if (Test-Path $BackupDirectory) {
            # Restore registry keys
            $RegFiles = Get-ChildItem -Path $BackupDirectory -Filter "*.reg"
            foreach ($RegFile in $RegFiles) {
                reg import $RegFile.FullName
                Write-Log "Restored registry from: $($RegFile.Name)" "SUCCESS"
            }

            # Restore file system artifacts
            $ZipFiles = Get-ChildItem -Path $BackupDirectory -Filter "*.zip"
            foreach ($ZipFile in $ZipFiles) {
                $ExtractPath = "C:\Program Files\VMware"
                Expand-Archive -Path $ZipFile.FullName -DestinationPath $ExtractPath -Force
                Write-Log "Restored files from: $($ZipFile.Name)" "SUCCESS"
            }

            Write-Log "Configuration restoration completed" "SUCCESS"
        }
        else {
            Write-Log "Backup directory not found: $BackupDirectory" "ERROR"
        }
    }
    catch {
        Write-Log "Failed to restore configuration: $($_.Exception.Message)" "ERROR"
    }
}
