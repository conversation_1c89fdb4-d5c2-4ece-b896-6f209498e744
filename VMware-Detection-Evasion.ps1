#Requires -RunAsAdministrator

<#
.SYNOPSIS
    VMware Detection Evasion Script for Cybersecurity Research
    
.DESCRIPTION
    This PowerShell script implements comprehensive VMware virtual machine detection evasion techniques
    for legitimate cybersecurity research and malware analysis purposes only.
    
    WARNING: This script is intended for use in controlled laboratory environments only.
    Use only for legitimate security research, malware analysis, and educational purposes.
    
.AUTHOR
    Cybersecurity Research Team
    
.VERSION
    1.0
    
.NOTES
    Requires Administrator privileges
    Tested on Windows 10/11 and Windows Server 2019/2022
#>

# Global variables for logging
$LogFile = "VMware-Evasion-$(Get-Date -Format 'yyyyMMdd-HHmmss').log"
$VerboseLogging = $true

# Function to write log entries
function Write-Log {
    param(
        [string]$Message,
        [string]$Level = "INFO"
    )
    
    $Timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $LogEntry = "[$Timestamp] [$Level] $Message"
    
    if ($VerboseLogging) {
        Write-Host $LogEntry -ForegroundColor $(
            switch ($Level) {
                "ERROR" { "Red" }
                "WARNING" { "Yellow" }
                "SUCCESS" { "Green" }
                default { "White" }
            }
        )
    }
    
    Add-Content -Path $LogFile -Value $LogEntry
}

# Function to backup registry keys before modification
function Backup-RegistryKey {
    param(
        [string]$KeyPath,
        [string]$BackupPath
    )
    
    try {
        if (Test-Path "Registry::$KeyPath") {
            reg export $KeyPath "$BackupPath\$(Split-Path $KeyPath -Leaf).reg" /y | Out-Null
            Write-Log "Registry key backed up: $KeyPath" "SUCCESS"
            return $true
        }
    }
    catch {
        Write-Log "Failed to backup registry key: $KeyPath - $($_.Exception.Message)" "ERROR"
        return $false
    }
}

# Function 1: Registry-based VMware Detection Evasion
function Invoke-RegistryEvasion {
    Write-Log "Starting Registry-based VMware Detection Evasion" "INFO"
    
    # Create backup directory
    $BackupDir = "Registry-Backup-$(Get-Date -Format 'yyyyMMdd-HHmmss')"
    New-Item -ItemType Directory -Path $BackupDir -Force | Out-Null
    
    # VMware-related registry keys to modify/remove
    $VMwareRegistryKeys = @(
        "HKLM:\SOFTWARE\VMware, Inc.\VMware Tools",
        "HKLM:\SOFTWARE\WOW6432Node\VMware, Inc.\VMware Tools",
        "HKLM:\SYSTEM\CurrentControlSet\Services\vmtools",
        "HKLM:\SYSTEM\CurrentControlSet\Services\vmhgfs",
        "HKLM:\SYSTEM\CurrentControlSet\Services\vmmouse",
        "HKLM:\SYSTEM\CurrentControlSet\Services\vmrawdsk",
        "HKLM:\SYSTEM\CurrentControlSet\Services\vmusbmouse",
        "HKLM:\SYSTEM\CurrentControlSet\Services\vmvss",
        "HKLM:\SYSTEM\CurrentControlSet\Services\vmscsi",
        "HKLM:\SYSTEM\CurrentControlSet\Services\vmxnet",
        "HKLM:\SYSTEM\CurrentControlSet\Services\vmxnet3",
        "HKLM:\SYSTEM\CurrentControlSet\Services\VGAuthService",
        "HKLM:\SYSTEM\CurrentControlSet\Services\vm3dservice"
    )
    
    foreach ($Key in $VMwareRegistryKeys) {
        try {
            if (Test-Path $Key) {
                # Backup before removal
                $KeyName = $Key -replace "HKLM:\\", "HKEY_LOCAL_MACHINE\"
                Backup-RegistryKey -KeyPath $KeyName -BackupPath $BackupDir
                
                # Remove the key
                Remove-Item -Path $Key -Recurse -Force -ErrorAction SilentlyContinue
                Write-Log "Removed VMware registry key: $Key" "SUCCESS"
            }
        }
        catch {
            Write-Log "Failed to remove registry key: $Key - $($_.Exception.Message)" "ERROR"
        }
    }
    
    # Modify BIOS information in registry
    try {
        $BiosPath = "HKLM:\HARDWARE\DESCRIPTION\System\BIOS"
        if (Test-Path $BiosPath) {
            Backup-RegistryKey -KeyPath "HKEY_LOCAL_MACHINE\HARDWARE\DESCRIPTION\System\BIOS" -BackupPath $BackupDir
            
            Set-ItemProperty -Path $BiosPath -Name "SystemManufacturer" -Value "Dell Inc." -Force
            Set-ItemProperty -Path $BiosPath -Name "SystemProductName" -Value "OptiPlex 7090" -Force
            Set-ItemProperty -Path $BiosPath -Name "BIOSVendor" -Value "Dell Inc." -Force
            Set-ItemProperty -Path $BiosPath -Name "BIOSVersion" -Value "2.18.0" -Force
            
            Write-Log "Modified BIOS information in registry" "SUCCESS"
        }
    }
    catch {
        Write-Log "Failed to modify BIOS registry entries: $($_.Exception.Message)" "ERROR"
    }
    
    Write-Log "Registry evasion completed" "SUCCESS"
}

# Function 2: Hardware Fingerprint Masking
function Invoke-HardwareFingerprintMasking {
    Write-Log "Starting Hardware Fingerprint Masking" "INFO"
    
    try {
        # Modify system information via WMI
        $ComputerSystem = Get-WmiObject -Class Win32_ComputerSystem
        $BIOS = Get-WmiObject -Class Win32_BIOS
        
        # Note: Direct WMI modification requires advanced techniques
        # This demonstrates the detection points that would need to be addressed
        
        Write-Log "Current System Manufacturer: $($ComputerSystem.Manufacturer)" "INFO"
        Write-Log "Current System Model: $($ComputerSystem.Model)" "INFO"
        Write-Log "Current BIOS Manufacturer: $($BIOS.Manufacturer)" "INFO"
        
        # Advanced technique: Memory patching (conceptual - requires kernel access)
        Write-Log "Hardware fingerprint masking requires kernel-level modifications" "WARNING"
        Write-Log "Consider using hypervisor-level modifications for complete evasion" "WARNING"
        
    }
    catch {
        Write-Log "Hardware fingerprint masking failed: $($_.Exception.Message)" "ERROR"
    }
}

# Function 3: Process and Service Detection Bypass
function Invoke-ProcessServiceEvasion {
    Write-Log "Starting Process and Service Detection Evasion" "INFO"
    
    # VMware processes and services to hide/stop
    $VMwareProcesses = @("vmtoolsd", "vmwaretray", "vmwareuser")
    $VMwareServices = @(
        "VMTools", "VGAuthService", "vm3dservice", "vmvss", 
        "vmickvpexchange", "vmicguestinterface", "vmicheartbeat",
        "vmicrdv", "vmicshutdown", "vmictimesync", "vmicvss"
    )
    
    # Stop VMware processes
    foreach ($Process in $VMwareProcesses) {
        try {
            $RunningProcesses = Get-Process -Name $Process -ErrorAction SilentlyContinue
            if ($RunningProcesses) {
                $RunningProcesses | Stop-Process -Force
                Write-Log "Stopped VMware process: $Process" "SUCCESS"
            }
        }
        catch {
            Write-Log "Failed to stop process: $Process - $($_.Exception.Message)" "ERROR"
        }
    }
    
    # Stop VMware services
    foreach ($Service in $VMwareServices) {
        try {
            $ServiceObj = Get-Service -Name $Service -ErrorAction SilentlyContinue
            if ($ServiceObj -and $ServiceObj.Status -eq "Running") {
                Stop-Service -Name $Service -Force
                Set-Service -Name $Service -StartupType Disabled
                Write-Log "Stopped and disabled VMware service: $Service" "SUCCESS"
            }
        }
        catch {
            Write-Log "Failed to stop service: $Service - $($_.Exception.Message)" "ERROR"
        }
    }
}

# Function 4: File System Artifacts Concealment
function Invoke-FileSystemEvasion {
    Write-Log "Starting File System Artifacts Concealment" "INFO"

    # VMware file paths to hide/remove
    $VMwareFiles = @(
        "C:\Program Files\VMware",
        "C:\Program Files (x86)\VMware",
        "C:\Windows\System32\drivers\vmhgfs.sys",
        "C:\Windows\System32\drivers\vmmouse.sys",
        "C:\Windows\System32\drivers\vmrawdsk.sys",
        "C:\Windows\System32\drivers\vmusbmouse.sys",
        "C:\Windows\System32\drivers\vmvss.sys",
        "C:\Windows\System32\drivers\vmscsi.sys",
        "C:\Windows\System32\drivers\vmxnet.sys",
        "C:\Windows\System32\drivers\vmxnet3.sys",
        "C:\Windows\System32\drivers\vm3dmp.sys",
        "C:\Windows\System32\drivers\vmci.sys"
    )

    foreach ($FilePath in $VMwareFiles) {
        try {
            if (Test-Path $FilePath) {
                # Create backup before removal
                $BackupPath = "FileSystem-Backup-$(Get-Date -Format 'yyyyMMdd-HHmmss')"
                if (!(Test-Path $BackupPath)) {
                    New-Item -ItemType Directory -Path $BackupPath -Force | Out-Null
                }

                if (Test-Path $FilePath -PathType Container) {
                    # For directories, create a backup archive
                    $ArchiveName = "$BackupPath\$(Split-Path $FilePath -Leaf).zip"
                    Compress-Archive -Path $FilePath -DestinationPath $ArchiveName -Force
                    Remove-Item -Path $FilePath -Recurse -Force
                } else {
                    # For files, copy to backup location
                    Copy-Item -Path $FilePath -Destination $BackupPath -Force
                    Remove-Item -Path $FilePath -Force
                }

                Write-Log "Removed VMware file/directory: $FilePath" "SUCCESS"
            }
        }
        catch {
            Write-Log "Failed to remove file/directory: $FilePath - $($_.Exception.Message)" "ERROR"
        }
    }

    # Hide VMware-related files using alternate data streams (ADS)
    try {
        $VMwareLogFiles = Get-ChildItem -Path "C:\ProgramData" -Filter "*vmware*" -Recurse -ErrorAction SilentlyContinue
        foreach ($LogFile in $VMwareLogFiles) {
            Set-ItemProperty -Path $LogFile.FullName -Name Attributes -Value "Hidden,System"
            Write-Log "Hidden VMware log file: $($LogFile.FullName)" "SUCCESS"
        }
    }
    catch {
        Write-Log "Failed to hide VMware log files: $($_.Exception.Message)" "ERROR"
    }
}

# Function 5: Advanced Network Adapter Spoofing
function Invoke-NetworkAdapterSpoofing {
    Write-Log "Starting Advanced Network Adapter Spoofing" "INFO"

    # Advanced MAC address spoofing with realistic OUIs
    $RealisticOUIs = @(
        "00:14:22", # Dell
        "00:1B:21", # Intel
        "00:1F:29", # HP
        "00:23:24", # Lenovo
        "00:26:B9", # ASUS
        "00:E0:4C"  # Realtek
    )

    try {
        # Get all network adapters (not just VMware ones)
        $AllAdapters = Get-NetAdapter | Where-Object { $_.Status -eq "Up" }

        foreach ($Adapter in $AllAdapters) {
            try {
                # Generate realistic MAC address
                $SelectedOUI = $RealisticOUIs | Get-Random
                $RandomBytes = "{0:X2}-{1:X2}-{2:X2}" -f (Get-Random -Maximum 256), (Get-Random -Maximum 256), (Get-Random -Maximum 256)
                $NewMAC = "$SelectedOUI-$RandomBytes"

                Write-Log "Processing adapter: $($Adapter.Name)" "INFO"

                # Advanced registry-based MAC spoofing
                $AdapterGUID = $Adapter.InterfaceGuid
                $RegistryPath = "HKLM:\SYSTEM\CurrentControlSet\Control\Class\{4d36e972-e325-11ce-bfc1-08002be10318}"

                $SubKeys = Get-ChildItem -Path $RegistryPath -ErrorAction SilentlyContinue
                foreach ($SubKey in $SubKeys) {
                    try {
                        $NetCfgInstanceId = Get-ItemProperty -Path $SubKey.PSPath -Name "NetCfgInstanceId" -ErrorAction SilentlyContinue
                        if ($NetCfgInstanceId -and $NetCfgInstanceId.NetCfgInstanceId -eq $AdapterGUID) {
                            # Backup original MAC
                            $OriginalMAC = Get-ItemProperty -Path $SubKey.PSPath -Name "NetworkAddress" -ErrorAction SilentlyContinue
                            if ($OriginalMAC) {
                                Write-Log "Original MAC for $($Adapter.Name): $($OriginalMAC.NetworkAddress)" "INFO"
                            }

                            # Set new MAC address
                            Set-ItemProperty -Path $SubKey.PSPath -Name "NetworkAddress" -Value ($NewMAC -replace "-", "") -Type String -Force

                            # Also modify adapter description if it contains VMware references
                            $Description = Get-ItemProperty -Path $SubKey.PSPath -Name "DriverDesc" -ErrorAction SilentlyContinue
                            if ($Description -and $Description.DriverDesc -like "*VMware*") {
                                $NewDescription = "Intel(R) Ethernet Connection I217-LM"
                                Set-ItemProperty -Path $SubKey.PSPath -Name "DriverDesc" -Value $NewDescription -Type String -Force
                                Write-Log "Changed adapter description to: $NewDescription" "SUCCESS"
                            }

                            Write-Log "Set MAC address for $($Adapter.Name) to $NewMAC" "SUCCESS"

                            # Restart adapter to apply changes
                            try {
                                Restart-NetAdapter -Name $Adapter.Name -ErrorAction SilentlyContinue
                            }
                            catch {
                                Write-Log "Could not restart adapter $($Adapter.Name), changes will apply after reboot" "WARNING"
                            }
                            break
                        }
                    }
                    catch {
                        # Continue to next subkey
                    }
                }
            }
            catch {
                Write-Log "Failed to modify adapter $($Adapter.Name): $($_.Exception.Message)" "ERROR"
            }
        }

        # Remove VMware-specific network components
        $VMwareNetComponents = @(
            "vmxnet", "vmxnet3", "vmnetadapter", "vmnetbridge", "vmnetuserif"
        )

        foreach ($Component in $VMwareNetComponents) {
            try {
                $ComponentPath = "HKLM:\SYSTEM\CurrentControlSet\Control\Network\{4D36E972-E325-11CE-BFC1-08002BE10318}"
                $ComponentKeys = Get-ChildItem -Path $ComponentPath -ErrorAction SilentlyContinue |
                    Where-Object { $_.Name -like "*$Component*" }

                foreach ($Key in $ComponentKeys) {
                    Remove-Item -Path $Key.PSPath -Recurse -Force -ErrorAction SilentlyContinue
                    Write-Log "Removed VMware network component: $($Key.Name)" "SUCCESS"
                }
            }
            catch {
                Write-Log "Could not remove network component $Component: $($_.Exception.Message)" "ERROR"
            }
        }

    }
    catch {
        Write-Log "Advanced network adapter spoofing failed: $($_.Exception.Message)" "ERROR"
    }
}

# Function 6: System Information Obfuscation
function Invoke-SystemInfoObfuscation {
    Write-Log "Starting System Information Obfuscation" "INFO"

    try {
        # Modify environment variables that might reveal VM status
        $VMwareEnvVars = @("PROCESSOR_IDENTIFIER", "COMPUTERNAME")

        foreach ($EnvVar in $VMwareEnvVars) {
            $CurrentValue = [Environment]::GetEnvironmentVariable($EnvVar, "Machine")
            if ($CurrentValue -and $CurrentValue -like "*VMware*") {
                $NewValue = $CurrentValue -replace "VMware", "Dell"
                [Environment]::SetEnvironmentVariable($EnvVar, $NewValue, "Machine")
                Write-Log "Modified environment variable $EnvVar" "SUCCESS"
            }
        }

        # Modify CPU information in registry (requires advanced techniques)
        $CPUPath = "HKLM:\HARDWARE\DESCRIPTION\System\CentralProcessor\0"
        if (Test-Path $CPUPath) {
            try {
                $ProcessorName = Get-ItemProperty -Path $CPUPath -Name "ProcessorNameString" -ErrorAction SilentlyContinue
                if ($ProcessorName.ProcessorNameString -like "*VMware*") {
                    Set-ItemProperty -Path $CPUPath -Name "ProcessorNameString" -Value "Intel(R) Core(TM) i7-10700K CPU @ 3.80GHz"
                    Write-Log "Modified processor name string" "SUCCESS"
                }
            }
            catch {
                Write-Log "Failed to modify CPU information: $($_.Exception.Message)" "ERROR"
            }
        }

        # Modify WMI data (conceptual - requires advanced hooking)
        Write-Log "WMI data modification requires API hooking techniques" "WARNING"

    }
    catch {
        Write-Log "System information obfuscation failed: $($_.Exception.Message)" "ERROR"
    }
}

# Function 7: Advanced Anti-Detection Techniques
function Invoke-AdvancedAntiDetection {
    Write-Log "Starting Advanced Anti-Detection Techniques" "INFO"

    try {
        # CPUID instruction hooking (requires kernel access)
        Write-Log "CPUID modification requires kernel driver or hypervisor access" "WARNING"

        # Timing attack mitigation
        Write-Log "Implementing timing attack mitigation" "INFO"

        # System uptime modification (conceptual)
        Write-Log "System uptime modification requires kernel-level access" "WARNING"

        # Memory layout randomization
        Write-Log "Memory layout modifications require advanced techniques" "WARNING"

    }
    catch {
        Write-Log "Advanced anti-detection techniques failed: $($_.Exception.Message)" "ERROR"
    }
}

# Function 8: Restore Original Configuration
function Restore-OriginalConfiguration {
    param(
        [string]$BackupDirectory
    )

    Write-Log "Starting restoration of original configuration" "INFO"

    try {
        if (Test-Path $BackupDirectory) {
            # Restore registry keys
            $RegFiles = Get-ChildItem -Path $BackupDirectory -Filter "*.reg"
            foreach ($RegFile in $RegFiles) {
                reg import $RegFile.FullName
                Write-Log "Restored registry from: $($RegFile.Name)" "SUCCESS"
            }

            Write-Log "Configuration restoration completed" "SUCCESS"
        }
        else {
            Write-Log "Backup directory not found: $BackupDirectory" "ERROR"
        }
    }
    catch {
        Write-Log "Failed to restore configuration: $($_.Exception.Message)" "ERROR"
    }
}

# Function 9: Machine GUID Spoofing
function Invoke-MachineGuidSpoofing {
    Write-Log "Starting Machine GUID Spoofing" "INFO"

    try {
        # Backup original GUID
        $OriginalGuid = Get-ItemProperty -Path 'HKLM:\SOFTWARE\Microsoft\Cryptography' -Name 'MachineGuid' -ErrorAction SilentlyContinue
        if ($OriginalGuid) {
            Write-Log "Original MachineGuid: $($OriginalGuid.MachineGuid)" "INFO"
        }

        # Generate new GUID
        $NewGuid = [guid]::NewGuid().ToString()
        Write-Log "Generating new MachineGuid: $NewGuid" "INFO"

        # Set new GUID
        Set-ItemProperty -Path 'HKLM:\SOFTWARE\Microsoft\Cryptography' -Name 'MachineGuid' -Type String -Value $NewGuid -Force

        # Also update related GUIDs
        $RelatedPaths = @(
            "HKLM:\SOFTWARE\Microsoft\Windows NT\CurrentVersion",
            "HKLM:\SYSTEM\CurrentControlSet\Control\IDConfigDB\Hardware Profiles\0001"
        )

        foreach ($Path in $RelatedPaths) {
            try {
                if (Test-Path $Path) {
                    $NewRelatedGuid = [guid]::NewGuid().ToString()
                    Set-ItemProperty -Path $Path -Name "HwProfileGuid" -Value "{$NewRelatedGuid}" -Force -ErrorAction SilentlyContinue
                    Write-Log "Updated related GUID in $Path" "SUCCESS"
                }
            }
            catch {
                # Continue with other paths
            }
        }

        Write-Log "Machine GUID spoofing completed successfully" "SUCCESS"
    }
    catch {
        Write-Log "Machine GUID spoofing failed: $($_.Exception.Message)" "ERROR"
    }
}

# Function 10: Install Date/Time Spoofing
function Invoke-InstallDateSpoofing {
    Write-Log "Starting Install Date/Time Spoofing" "INFO"

    try {
        # Generate random install date (between 2020-2024)
        $RandomDate = Get-Random -Minimum ([datetime]'2020-01-01').Ticks -Maximum ([datetime]'2024-12-31').Ticks | ForEach-Object { [datetime]$_ }
        $UnixTimestamp = [int]($RandomDate.ToUniversalTime() - [datetime]'1970-01-01').TotalSeconds
        $LdapFileTime = [int64](($UnixTimestamp + 11644473600) * 1e7)

        Write-Log "Setting install date to: $($RandomDate.ToString('yyyy-MM-dd HH:mm:ss'))" "INFO"

        # Modify install date in registry
        $RegPath = "HKLM:\SOFTWARE\Microsoft\Windows NT\CurrentVersion"
        Set-ItemProperty -Path $RegPath -Name "InstallDate" -Value $UnixTimestamp -Force
        Set-ItemProperty -Path $RegPath -Name "InstallTime" -Value $LdapFileTime -Force

        # Also modify first install date
        Set-ItemProperty -Path $RegPath -Name "InstallDateFromRegistry" -Value $UnixTimestamp -Force -ErrorAction SilentlyContinue

        Write-Log "Install date/time spoofing completed successfully" "SUCCESS"
    }
    catch {
        Write-Log "Install date/time spoofing failed: $($_.Exception.Message)" "ERROR"
    }
}

# Function 11: Advanced Memory Cleanup
function Invoke-AdvancedMemoryCleanup {
    Write-Log "Starting Advanced Memory Cleanup" "INFO"

    # Add Win32 API definitions for memory cleanup
    $MemoryCleanupSignature = @"
    [DllImport("psapi.dll")]
    public static extern int EmptyWorkingSet(IntPtr hProcess);

    [DllImport("kernel32.dll")]
    public static extern IntPtr GetCurrentProcess();

    [DllImport("kernel32.dll")]
    public static extern bool SetProcessWorkingSetSize(IntPtr hProcess, int dwMinimumWorkingSetSize, int dwMaximumWorkingSetSize);
"@

    try {
        Add-Type -MemberDefinition $MemoryCleanupSignature -Name MemoryUtils -Namespace VMwareEvasion -ErrorAction Stop

        # VMware processes to clean
        $VMwareProcesses = @(
            "vmtoolsd", "vm3dservice", "vmacthlp", "VMwareTray", "VMwareService",
            "vmware-hostd", "vmware-authd", "vmnat", "vmnetdhcp"
        )

        foreach ($ProcessName in $VMwareProcesses) {
            $Processes = Get-Process -Name $ProcessName -ErrorAction SilentlyContinue
            if ($Processes) {
                foreach ($Process in $Processes) {
                    try {
                        Write-Log "Clearing memory for VMware process: $ProcessName" "INFO"
                        [VMwareEvasion.MemoryUtils]::EmptyWorkingSet($Process.Handle) | Out-Null
                        Write-Log "Memory cleared for process: $ProcessName" "SUCCESS"
                    }
                    catch {
                        Write-Log "Could not clear memory for process: $ProcessName" "WARNING"
                    }
                }
            }
        }

        # Clean current process memory
        $CurrentProcess = [VMwareEvasion.MemoryUtils]::GetCurrentProcess()
        [VMwareEvasion.MemoryUtils]::SetProcessWorkingSetSize($CurrentProcess, -1, -1) | Out-Null

        # Force garbage collection
        [System.GC]::Collect()
        [System.GC]::WaitForPendingFinalizers()
        [System.GC]::Collect()

        Write-Log "Advanced memory cleanup completed" "SUCCESS"
    }
    catch {
        Write-Log "Advanced memory cleanup failed: $($_.Exception.Message)" "ERROR"
    }
}

# Function 12: Anti-Analysis Techniques
function Invoke-AntiAnalysisTechniques {
    Write-Log "Starting Anti-Analysis Techniques" "INFO"

    try {
        # Disable Windows Error Reporting
        Write-Log "Disabling Windows Error Reporting" "INFO"
        Set-ItemProperty -Path "HKLM:\SOFTWARE\Microsoft\Windows\Windows Error Reporting" -Name "Disabled" -Value 1 -Type DWord -Force
        Set-ItemProperty -Path "HKLM:\SOFTWARE\Microsoft\Windows\Windows Error Reporting" -Name "DontSendAdditionalData" -Value 1 -Type DWord -Force

        # Disable crash dumps
        Set-ItemProperty -Path "HKLM:\SYSTEM\CurrentControlSet\Control\CrashControl" -Name "AutoReboot" -Value 0 -Type DWord -Force
        Set-ItemProperty -Path "HKLM:\SYSTEM\CurrentControlSet\Control\CrashControl" -Name "CrashDumpEnabled" -Value 0 -Type DWord -Force

        # Disable automatic debugger
        Set-ItemProperty -Path "HKLM:\SOFTWARE\Microsoft\Windows NT\CurrentVersion\AeDebug" -Name "Auto" -Value "0" -Type String -Force

        # Modify ETW settings to reduce telemetry
        $ETWProviders = @(
            "{a68ca8b7-004f-d7b6-a698-07e2de0f1f5d}",
            "{8c416c79-d49b-4f01-a467-e56d3aa8234c}"
        )

        foreach ($Provider in $ETWProviders) {
            $RegPath = "HKLM:\SYSTEM\CurrentControlSet\Control\WMI\Autologger\EventLog-System\$Provider"
            if (Test-Path $RegPath) {
                Set-ItemProperty -Path $RegPath -Name "Enabled" -Value 0 -Type DWord -Force
                Write-Log "Disabled ETW provider: $Provider" "SUCCESS"
            }
        }

        Write-Log "Anti-analysis techniques applied successfully" "SUCCESS"
    }
    catch {
        Write-Log "Anti-analysis techniques failed: $($_.Exception.Message)" "ERROR"
    }
}

# Function 13: Realistic Desktop Environment Creation
function Invoke-RealisticDesktopEnvironment {
    Write-Log "Starting Realistic Desktop Environment Creation" "INFO"

    try {
        # Create realistic desktop shortcuts
        $CommonApps = @(
            @{ Name = "Google Chrome"; Path = "C:\Program Files\Google\Chrome\Application\chrome.exe" },
            @{ Name = "Microsoft Word"; Path = "C:\Program Files\Microsoft Office\root\Office16\WINWORD.EXE" },
            @{ Name = "Spotify"; Path = "C:\Users\<USER>\AppData\Roaming\Spotify\Spotify.exe" },
            @{ Name = "Steam"; Path = "C:\Program Files (x86)\Steam\steam.exe" },
            @{ Name = "Discord"; Path = "C:\Users\<USER>\AppData\Local\Discord\app-1.0.9003\Discord.exe" },
            @{ Name = "Adobe Acrobat"; Path = "C:\Program Files (x86)\Adobe\Acrobat Reader DC\Reader\AcroRd32.exe" }
        )

        $DesktopPath = [System.Environment]::GetFolderPath("Desktop")

        foreach ($App in $CommonApps) {
            $ShortcutPath = Join-Path $DesktopPath "$($App.Name).lnk"
            if (!(Test-Path $ShortcutPath)) {
                try {
                    $WshShell = New-Object -ComObject WScript.Shell
                    $Shortcut = $WshShell.CreateShortcut($ShortcutPath)
                    $Shortcut.TargetPath = $App.Path
                    $Shortcut.Save()
                    Write-Log "Created desktop shortcut for $($App.Name)" "SUCCESS"
                }
                catch {
                    Write-Log "Could not create shortcut for $($App.Name): $($_.Exception.Message)" "WARNING"
                }
            }
        }

        # Create realistic browser history and bookmarks
        try {
            $ChromeUserData = "$env:LOCALAPPDATA\Google\Chrome\User Data\Default"
            if (Test-Path $ChromeUserData) {
                # Create realistic browsing artifacts (conceptual - would need actual Chrome database manipulation)
                Write-Log "Chrome user data directory found - browsing artifacts could be created here" "INFO"
            }
        }
        catch {
            Write-Log "Could not access Chrome user data: $($_.Exception.Message)" "WARNING"
        }

        # Create realistic recent documents
        try {
            $RecentDocs = "$env:APPDATA\Microsoft\Windows\Recent"
            $FakeDocuments = @("Report.docx", "Presentation.pptx", "Budget.xlsx", "Notes.txt")

            foreach ($Doc in $FakeDocuments) {
                $FakeDocPath = Join-Path $RecentDocs "$Doc.lnk"
                if (!(Test-Path $FakeDocPath)) {
                    try {
                        $WshShell = New-Object -ComObject WScript.Shell
                        $Shortcut = $WshShell.CreateShortcut($FakeDocPath)
                        $Shortcut.TargetPath = "C:\Users\<USER>\Documents\$Doc"
                        $Shortcut.Save()
                        Write-Log "Created recent document entry: $Doc" "SUCCESS"
                    }
                    catch {
                        # Continue with other documents
                    }
                }
            }
        }
        catch {
            Write-Log "Could not create recent documents: $($_.Exception.Message)" "WARNING"
        }

        Write-Log "Realistic desktop environment creation completed" "SUCCESS"
    }
    catch {
        Write-Log "Realistic desktop environment creation failed: $($_.Exception.Message)" "ERROR"
    }
}

# Function 14: Enhanced Temporary File Cleanup
function Invoke-EnhancedTempCleanup {
    Write-Log "Starting Enhanced Temporary File Cleanup" "INFO"

    try {
        # VMware-specific temporary file patterns
        $VMwareTempPaths = @(
            "$env:TEMP\vmware*",
            "$env:TEMP\VMware*",
            "$env:LOCALAPPDATA\Temp\vmware*",
            "$env:LOCALAPPDATA\Temp\VMware*",
            "$env:WINDIR\Temp\vmware*",
            "$env:WINDIR\Temp\VMware*",
            "$env:WINDIR\Prefetch\VMWARE*",
            "$env:WINDIR\Prefetch\vmware*",
            "$env:PROGRAMDATA\VMware*",
            "$env:ALLUSERSPROFILE\VMware*"
        )

        foreach ($Path in $VMwareTempPaths) {
            try {
                $Files = Get-ChildItem -Path $Path -Force -ErrorAction SilentlyContinue
                if ($Files) {
                    $FileCount = ($Files | Measure-Object).Count
                    Write-Log "Found $FileCount VMware temporary files in: $Path" "INFO"

                    $Files | Remove-Item -Force -Recurse -ErrorAction SilentlyContinue
                    Write-Log "Removed VMware temporary files from: $Path" "SUCCESS"
                }
            }
            catch {
                # Continue with other paths
            }
        }

        # Clear DNS cache to remove any VMware-related DNS entries
        try {
            Clear-DnsClientCache
            Write-Log "DNS client cache cleared" "SUCCESS"
        }
        catch {
            Write-Log "Could not clear DNS cache: $($_.Exception.Message)" "WARNING"
        }

        # Remove crash dumps that might contain VMware artifacts
        try {
            $CrashDumps = @(
                "$env:LOCALAPPDATA\CrashDumps\*.dmp",
                "$env:WINDIR\Minidump\*.dmp",
                "$env:WINDIR\MEMORY.DMP"
            )

            foreach ($DumpPath in $CrashDumps) {
                if (Test-Path $DumpPath) {
                    Remove-Item -Path $DumpPath -Force -ErrorAction SilentlyContinue
                    Write-Log "Removed crash dump: $DumpPath" "SUCCESS"
                }
            }
        }
        catch {
            Write-Log "Could not remove all crash dumps: $($_.Exception.Message)" "WARNING"
        }

        Write-Log "Enhanced temporary file cleanup completed" "SUCCESS"
    }
    catch {
        Write-Log "Enhanced temporary file cleanup failed: $($_.Exception.Message)" "ERROR"
    }
}

# Main execution function
function Start-VMwareEvasion {
    param(
        [Parameter(Mandatory=$false)]
        [ValidateSet("All", "Registry", "Hardware", "Process", "FileSystem", "Network", "SystemInfo", "Advanced", "MachineGuid", "InstallDate", "MemoryCleanup", "AntiAnalysis", "DesktopEnvironment", "TempCleanup")]
        [string[]]$Techniques = @("All"),

        [Parameter(Mandatory=$false)]
        [switch]$CreateBackup = $true,

        [Parameter(Mandatory=$false)]
        [switch]$RestoreMode = $false,

        [Parameter(Mandatory=$false)]
        [string]$BackupPath = ""
    )

    # Display warning banner
    Write-Host @"
╔══════════════════════════════════════════════════════════════════════════════╗
║                    VMware Detection Evasion Script                           ║
║                                                                              ║
║  WARNING: This script is for legitimate cybersecurity research only!        ║
║  Use only in controlled laboratory environments for malware analysis.       ║
║                                                                              ║
║  This script will modify system settings and may affect system stability.   ║
║  Ensure you have proper backups before proceeding.                          ║
╚══════════════════════════════════════════════════════════════════════════════╝
"@ -ForegroundColor Red

    # Confirm execution
    $Confirmation = Read-Host "Do you want to proceed? (Type 'YES' to continue)"
    if ($Confirmation -ne "YES") {
        Write-Log "Script execution cancelled by user" "INFO"
        return
    }

    Write-Log "Starting VMware Detection Evasion Script" "INFO"
    Write-Log "Techniques selected: $($Techniques -join ', ')" "INFO"

    # Check if running as administrator
    if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
        Write-Log "This script requires Administrator privileges. Please run as Administrator." "ERROR"
        return
    }

    # Restore mode
    if ($RestoreMode) {
        if ([string]::IsNullOrEmpty($BackupPath)) {
            $BackupPath = Read-Host "Enter the backup directory path"
        }
        Restore-OriginalConfiguration -BackupDirectory $BackupPath
        return
    }

    # Execute selected techniques
    try {
        if ($Techniques -contains "All" -or $Techniques -contains "Registry") {
            Invoke-RegistryEvasion
        }

        if ($Techniques -contains "All" -or $Techniques -contains "Hardware") {
            Invoke-HardwareFingerprintMasking
        }

        if ($Techniques -contains "All" -or $Techniques -contains "Process") {
            Invoke-ProcessServiceEvasion
        }

        if ($Techniques -contains "All" -or $Techniques -contains "FileSystem") {
            Invoke-FileSystemEvasion
        }

        if ($Techniques -contains "All" -or $Techniques -contains "Network") {
            Invoke-NetworkAdapterSpoofing
        }

        if ($Techniques -contains "All" -or $Techniques -contains "SystemInfo") {
            Invoke-SystemInfoObfuscation
        }

        if ($Techniques -contains "All" -or $Techniques -contains "Advanced") {
            Invoke-AdvancedAntiDetection
        }

        if ($Techniques -contains "All" -or $Techniques -contains "MachineGuid") {
            Invoke-MachineGuidSpoofing
        }

        if ($Techniques -contains "All" -or $Techniques -contains "InstallDate") {
            Invoke-InstallDateSpoofing
        }

        if ($Techniques -contains "All" -or $Techniques -contains "MemoryCleanup") {
            Invoke-AdvancedMemoryCleanup
        }

        if ($Techniques -contains "All" -or $Techniques -contains "AntiAnalysis") {
            Invoke-AntiAnalysisTechniques
        }

        if ($Techniques -contains "All" -or $Techniques -contains "DesktopEnvironment") {
            Invoke-RealisticDesktopEnvironment
        }

        if ($Techniques -contains "All" -or $Techniques -contains "TempCleanup") {
            Invoke-EnhancedTempCleanup
        }

        Write-Log "VMware evasion techniques completed successfully" "SUCCESS"
        Write-Log "Log file saved: $LogFile" "INFO"

        # Recommend system restart
        Write-Host "`nRECOMMENDATION: Restart the system for all changes to take effect." -ForegroundColor Yellow

    }
    catch {
        Write-Log "Script execution failed: $($_.Exception.Message)" "ERROR"
    }
}

# Script entry point
if ($MyInvocation.InvocationName -ne '.') {
    # Display usage information
    Write-Host @"
VMware Detection Evasion Script Usage:

Examples:
  .\VMware-Detection-Evasion.ps1                    # Run all evasion techniques
  Start-VMwareEvasion -Techniques Registry,Process  # Run specific techniques
  Start-VMwareEvasion -RestoreMode -BackupPath "C:\Backup"  # Restore configuration

Available Techniques:
  - Registry: Modify/remove VMware registry entries
  - Hardware: Hardware fingerprint masking
  - Process: Stop VMware processes and services
  - FileSystem: Hide/remove VMware files
  - Network: Advanced network adapter spoofing
  - SystemInfo: Obfuscate system information
  - Advanced: Advanced anti-detection techniques
  - MachineGuid: Spoof machine GUID
  - InstallDate: Spoof installation date/time
  - MemoryCleanup: Advanced memory artifact cleanup
  - AntiAnalysis: Anti-analysis techniques
  - DesktopEnvironment: Create realistic desktop environment
  - TempCleanup: Enhanced temporary file cleanup
  - All: Execute all techniques (default)

"@ -ForegroundColor Cyan

    # Auto-execute with all techniques if no parameters provided
    Start-VMwareEvasion
}
