#Requires -RunAsAdministrator

<#
.SYNOPSIS
    VMware Detection Test Script
    
.DESCRIPTION
    This script tests various VMware detection methods to verify the effectiveness
    of evasion techniques. Use this to validate that the evasion script is working correctly.
    
.NOTES
    For legitimate cybersecurity research and testing purposes only.
#>

function Test-VMwareDetection {
    Write-Host "VMware Detection Test Results" -ForegroundColor Cyan
    Write-Host "=============================" -ForegroundColor Cyan
    
    $DetectionResults = @{}
    
    # Test 1: Registry-based Detection
    Write-Host "`n[1] Registry-based Detection Tests:" -ForegroundColor Yellow
    
    $RegistryKeys = @(
        "HKLM:\SOFTWARE\VMware, Inc.\VMware Tools",
        "HKLM:\SYSTEM\CurrentControlSet\Services\vmtools",
        "HKLM:\SYSTEM\CurrentControlSet\Services\VGAuthService"
    )
    
    $RegistryDetected = $false
    foreach ($Key in $RegistryKeys) {
        if (Test-Path $Key) {
            Write-Host "  ❌ DETECTED: $Key exists" -ForegroundColor Red
            $RegistryDetected = $true
        } else {
            Write-Host "  ✅ EVADED: $Key not found" -ForegroundColor Green
        }
    }
    $DetectionResults["Registry"] = -not $RegistryDetected
    
    # Test 2: Process-based Detection
    Write-Host "`n[2] Process-based Detection Tests:" -ForegroundColor Yellow
    
    $VMwareProcesses = @("vmtoolsd", "vmwaretray", "vmwareuser")
    $ProcessDetected = $false
    
    foreach ($ProcessName in $VMwareProcesses) {
        $Process = Get-Process -Name $ProcessName -ErrorAction SilentlyContinue
        if ($Process) {
            Write-Host "  ❌ DETECTED: Process '$ProcessName' is running" -ForegroundColor Red
            $ProcessDetected = $true
        } else {
            Write-Host "  ✅ EVADED: Process '$ProcessName' not found" -ForegroundColor Green
        }
    }
    $DetectionResults["Process"] = -not $ProcessDetected
    
    # Test 3: Service-based Detection
    Write-Host "`n[3] Service-based Detection Tests:" -ForegroundColor Yellow
    
    $VMwareServices = @("VMTools", "VGAuthService", "vm3dservice")
    $ServiceDetected = $false
    
    foreach ($ServiceName in $VMwareServices) {
        $Service = Get-Service -Name $ServiceName -ErrorAction SilentlyContinue
        if ($Service -and $Service.Status -eq "Running") {
            Write-Host "  ❌ DETECTED: Service '$ServiceName' is running" -ForegroundColor Red
            $ServiceDetected = $true
        } else {
            Write-Host "  ✅ EVADED: Service '$ServiceName' not running or not found" -ForegroundColor Green
        }
    }
    $DetectionResults["Service"] = -not $ServiceDetected
    
    # Test 4: File System Detection
    Write-Host "`n[4] File System Detection Tests:" -ForegroundColor Yellow
    
    $VMwareFiles = @(
        "C:\Program Files\VMware",
        "C:\Windows\System32\drivers\vmhgfs.sys",
        "C:\Windows\System32\drivers\vmxnet3.sys"
    )
    
    $FileDetected = $false
    foreach ($FilePath in $VMwareFiles) {
        if (Test-Path $FilePath) {
            Write-Host "  ❌ DETECTED: $FilePath exists" -ForegroundColor Red
            $FileDetected = $true
        } else {
            Write-Host "  ✅ EVADED: $FilePath not found" -ForegroundColor Green
        }
    }
    $DetectionResults["FileSystem"] = -not $FileDetected
    
    # Test 5: WMI-based Detection
    Write-Host "`n[5] WMI-based Detection Tests:" -ForegroundColor Yellow
    
    try {
        $ComputerSystem = Get-WmiObject -Class Win32_ComputerSystem
        $BIOS = Get-WmiObject -Class Win32_BIOS
        
        $WMIDetected = $false
        
        # Check manufacturer
        if ($ComputerSystem.Manufacturer -like "*VMware*") {
            Write-Host "  ❌ DETECTED: System Manufacturer contains 'VMware'" -ForegroundColor Red
            $WMIDetected = $true
        } else {
            Write-Host "  ✅ EVADED: System Manufacturer: $($ComputerSystem.Manufacturer)" -ForegroundColor Green
        }
        
        # Check model
        if ($ComputerSystem.Model -like "*VMware*") {
            Write-Host "  ❌ DETECTED: System Model contains 'VMware'" -ForegroundColor Red
            $WMIDetected = $true
        } else {
            Write-Host "  ✅ EVADED: System Model: $($ComputerSystem.Model)" -ForegroundColor Green
        }
        
        # Check BIOS
        if ($BIOS.Manufacturer -like "*VMware*") {
            Write-Host "  ❌ DETECTED: BIOS Manufacturer contains 'VMware'" -ForegroundColor Red
            $WMIDetected = $true
        } else {
            Write-Host "  ✅ EVADED: BIOS Manufacturer: $($BIOS.Manufacturer)" -ForegroundColor Green
        }
        
        $DetectionResults["WMI"] = -not $WMIDetected
    }
    catch {
        Write-Host "  ⚠️ ERROR: WMI query failed - $($_.Exception.Message)" -ForegroundColor Yellow
        $DetectionResults["WMI"] = $null
    }
    
    # Test 6: Network Adapter Detection
    Write-Host "`n[6] Network Adapter Detection Tests:" -ForegroundColor Yellow
    
    try {
        $NetworkAdapters = Get-NetAdapter | Where-Object { $_.Status -eq "Up" }
        $NetworkDetected = $false
        
        foreach ($Adapter in $NetworkAdapters) {
            if ($Adapter.InterfaceDescription -like "*VMware*" -or $Adapter.InterfaceDescription -like "*vmxnet*") {
                Write-Host "  ❌ DETECTED: VMware network adapter found: $($Adapter.InterfaceDescription)" -ForegroundColor Red
                $NetworkDetected = $true
            } else {
                Write-Host "  ✅ EVADED: Network adapter: $($Adapter.InterfaceDescription)" -ForegroundColor Green
            }
        }
        
        $DetectionResults["Network"] = -not $NetworkDetected
    }
    catch {
        Write-Host "  ⚠️ ERROR: Network adapter query failed - $($_.Exception.Message)" -ForegroundColor Yellow
        $DetectionResults["Network"] = $null
    }
    
    # Test 7: MAC Address Detection
    Write-Host "`n[7] MAC Address Detection Tests:" -ForegroundColor Yellow
    
    try {
        $NetworkAdapters = Get-NetAdapter | Where-Object { $_.Status -eq "Up" }
        $MACDetected = $false
        
        # VMware MAC address prefixes
        $VMwareMACPrefixes = @("00:0C:29", "00:1C:14", "00:50:56")
        
        foreach ($Adapter in $NetworkAdapters) {
            $MAC = $Adapter.MacAddress
            foreach ($Prefix in $VMwareMACPrefixes) {
                if ($MAC -like "$Prefix*") {
                    Write-Host "  ❌ DETECTED: VMware MAC address detected: $MAC" -ForegroundColor Red
                    $MACDetected = $true
                    break
                }
            }
            if (-not $MACDetected) {
                Write-Host "  ✅ EVADED: MAC address: $MAC" -ForegroundColor Green
            }
        }
        
        $DetectionResults["MAC"] = -not $MACDetected
    }
    catch {
        Write-Host "  ⚠️ ERROR: MAC address query failed - $($_.Exception.Message)" -ForegroundColor Yellow
        $DetectionResults["MAC"] = $null
    }
    
    # Summary
    Write-Host "`n" + "="*50 -ForegroundColor Cyan
    Write-Host "DETECTION EVASION SUMMARY" -ForegroundColor Cyan
    Write-Host "="*50 -ForegroundColor Cyan
    
    $TotalTests = 0
    $PassedTests = 0
    
    foreach ($Test in $DetectionResults.Keys) {
        $Result = $DetectionResults[$Test]
        $TotalTests++
        
        if ($Result -eq $true) {
            Write-Host "✅ $Test Detection: EVADED" -ForegroundColor Green
            $PassedTests++
        } elseif ($Result -eq $false) {
            Write-Host "❌ $Test Detection: DETECTED" -ForegroundColor Red
        } else {
            Write-Host "⚠️ $Test Detection: ERROR/UNKNOWN" -ForegroundColor Yellow
        }
    }
    
    $SuccessRate = [math]::Round(($PassedTests / $TotalTests) * 100, 1)
    Write-Host "`nOverall Evasion Success Rate: $SuccessRate% ($PassedTests/$TotalTests)" -ForegroundColor $(
        if ($SuccessRate -ge 80) { "Green" } 
        elseif ($SuccessRate -ge 60) { "Yellow" } 
        else { "Red" }
    )
    
    if ($SuccessRate -lt 100) {
        Write-Host "`n⚠️ RECOMMENDATION: Run additional evasion techniques for detected vectors" -ForegroundColor Yellow
    } else {
        Write-Host "`n🎉 EXCELLENT: All detection vectors successfully evaded!" -ForegroundColor Green
    }
    
    return $DetectionResults
}

# Run the test if script is executed directly
if ($MyInvocation.InvocationName -ne '.') {
    Test-VMwareDetection
}
